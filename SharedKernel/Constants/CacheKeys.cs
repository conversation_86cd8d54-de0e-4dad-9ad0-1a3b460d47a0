namespace SharedKernel.Constants;

/// <summary>
/// Konstanty pro klíče cache používané napříč aplikací.
/// Centralizace klíčů umožňuje snadnou správu a předchází duplicitám.
/// </summary>
public static class CacheKeys
{
    /// <summary>
    /// Výchozí prefix pro cache klíče (používá se jako fallback).
    /// </summary>
    public const string DefaultAppPrefix = "DataCapture";

    /// <summary>
    /// Separator používaný v cache klíčích.
    /// </summary>
    public const string Separator = "_";

    /// <summary>
    /// Vytvoří cache klíč s výchozím prefixem (pro zpětnou kompatibilitu).
    /// Doporučuje se používat ICacheKeyProvider pro konfigurovatelné klíče.
    /// </summary>
    /// <param name="key"><PERSON>áklad<PERSON><PERSON> kl<PERSON></param>
    /// <returns>Kompletní cache klíč s výchozím prefixem</returns>
    [Obsolete("Použijte ICacheKeyProvider pro konfigurovatelné cache klíče")]
    public static string WithPrefix(string key) => $"{DefaultAppPrefix}{Separator}{key}";

    /// <summary>
    /// Vytvoří cache klíč pro entitu s výchozím prefixem (pro zpětnou kompatibilitu).
    /// Doporučuje se používat ICacheKeyProvider pro konfigurovatelné klíče.
    /// </summary>
    /// <param name="entityName">Název entity</param>
    /// <param name="operation">Typ operace (GetAll, GetPaged, atd.)</param>
    /// <returns>Cache klíč pro entitu</returns>
    [Obsolete("Použijte ICacheKeyProvider pro konfigurovatelné cache klíče")]
    public static string ForEntity(string entityName, string operation)
        => WithPrefix($"{operation}{Separator}{entityName}");

    /// <summary>
    /// Vytvoří cache klíč pro stránkovaný dotaz s výchozím prefixem (pro zpětnou kompatibilitu).
    /// Doporučuje se používat ICacheKeyProvider pro konfigurovatelné klíče.
    /// </summary>
    /// <param name="entityName">Název entity</param>
    /// <param name="pageNumber">Číslo stránky</param>
    /// <param name="pageSize">Velikost stránky</param>
    /// <param name="sortBy">Řazení podle</param>
    /// <param name="sortDescending">Sestupné řazení</param>
    /// <returns>Cache klíč pro stránkovaný dotaz</returns>
    [Obsolete("Použijte ICacheKeyProvider pro konfigurovatelné cache klíče")]
    public static string ForPagedQuery(string entityName, int pageNumber, int pageSize,
        string? sortBy = null, bool sortDescending = false)
    {
        var key = $"GetPaged{Separator}{entityName}{Separator}Page{pageNumber}{Separator}Size{pageSize}";

        if (!string.IsNullOrEmpty(sortBy))
        {
            key += $"{Separator}Sort{sortBy}";
            if (sortDescending)
                key += "Desc";
        }

        return WithPrefix(key);
    }

    /// <summary>
    /// Vytvoří cache klíč pro uživatelská data s výchozím prefixem (pro zpětnou kompatibilitu).
    /// Doporučuje se používat ICacheKeyProvider pro konfigurovatelné klíče.
    /// </summary>
    /// <param name="userId">ID uživatele</param>
    /// <param name="dataType">Typ dat</param>
    /// <returns>Cache klíč pro uživatelská data</returns>
    [Obsolete("Použijte ICacheKeyProvider pro konfigurovatelné cache klíče")]
    public static string ForUser(string userId, string dataType)
        => WithPrefix($"User{Separator}{userId}{Separator}{dataType}");
}
