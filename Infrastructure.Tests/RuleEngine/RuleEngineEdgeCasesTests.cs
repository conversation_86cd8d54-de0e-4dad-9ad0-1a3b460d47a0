using Infrastructure.RuleEngine;
using Infrastructure.RuleEngine.API;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Infrastructure.Tests.RuleEngine;

/// <summary>
/// Testy pro edge cases a error scenarios v RuleEngine systému.
/// Ověřuje robustnost a správné chování v neočekávaných situacích.
/// </summary>
public class RuleEngineEdgeCasesTests
{
    private readonly CalculationEngine _engine;
    private readonly TestRuleDataProvider _dataProvider;
    private readonly IReadOnlyDictionary<string, Type> _entityTypeMap;

    public RuleEngineEdgeCasesTests()
    {
        _entityTypeMap = new Dictionary<string, Type>
        {
            ["TestEntity"] = typeof(TestEntity),
            ["EmptyEntity"] = typeof(EmptyEntity),
            ["ComplexEntity"] = typeof(ComplexEntity)
        };

        _dataProvider = new TestRuleDataProvider();
        var expressionBuilder = new ExpressionBuilder(_dataProvider, _entityTypeMap);
        var mockLogger = new Mock<ILogger<CalculationEngine>>();
        _engine = new CalculationEngine(expressionBuilder, _entityTypeMap, mockLogger.Object);
    }

    [Fact]
    public void Execute_DivisionByZero_ThrowsRuleExecutionException()
    {
        // Arrange - Pravidlo: 10 / 0
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Division by Zero",
            TargetEntityName = "TestEntity",
            RootNode = new OperationNode
            {
                Operator = OperatorType.Divide,
                Operands = new List<RuleNode>
                {
                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Integer, Value = "10" },
                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Integer, Value = "0" }
                }
            }
        };

        var entity = new TestEntity { Value = 5 };

        // Act & Assert
        var exception = Assert.Throws<RuleExecutionException>(() => _engine.Execute(rule, entity));
        Assert.Contains("Division by Zero", exception.Message);
    }

    [Fact]
    public void Execute_InvalidPropertyPath_ThrowsRuleExecutionException()
    {
        // Arrange - Pravidlo s neexistující vlastností
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Invalid Property",
            TargetEntityName = "TestEntity",
            RootNode = new SourceValueNode { SourcePath = "NonExistentProperty" }
        };

        var entity = new TestEntity { Value = 5 };

        // Act & Assert
        var exception = Assert.Throws<RuleExecutionException>(() => _engine.Execute(rule, entity));
        Assert.Contains("Invalid Property", exception.Message);
    }

    [Fact]
    public void Execute_NullPropertyNavigation_HandlesGracefully()
    {
        // Arrange - Pravidlo: entity.NullProperty.Value
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Null Navigation",
            TargetEntityName = "ComplexEntity",
            RootNode = new SourceValueNode { SourcePath = "NullProperty.Value" }
        };

        var entity = new ComplexEntity { NullProperty = null };

        // Act & Assert
        var exception = Assert.Throws<RuleExecutionException>(() => _engine.Execute(rule, entity));
        Assert.Contains("Null Navigation", exception.Message);
    }

    [Fact]
    public void Execute_EmptyCollection_ReturnsZeroForCount()
    {
        // Arrange - Pravidlo: Count(entity.EmptyList)
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Empty Collection Count",
            TargetEntityName = "ComplexEntity",
            RootNode = new AggregationNode
            {
                AggregationType = AggregationType.Count,
                CollectionPath = "EmptyList"
            }
        };

        var entity = new ComplexEntity { EmptyList = new List<TestEntity>() };

        // Act
        var result = _engine.Execute(rule, entity);

        // Assert
        Assert.Equal(0, result);
    }

    [Fact]
    public void Execute_VeryLargeNumbers_HandlesCorrectly()
    {
        // Arrange - Pravidlo s velkými čísly
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Large Numbers",
            TargetEntityName = "TestEntity",
            RootNode = new OperationNode
            {
                Operator = OperatorType.Add,
                Operands = new List<RuleNode>
                {
                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "************.99" },
                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "0.01" }
                }
            }
        };

        var entity = new TestEntity { Value = 5 };

        // Act
        var result = _engine.Execute(rule, entity);

        // Assert
        Assert.Equal(1000000000000.00m, result);
    }

    [Fact]
    public void Execute_InvalidDateTimeFormat_ThrowsRuleExecutionException()
    {
        // Arrange - Pravidlo s neplatným DateTime formátem
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Invalid DateTime",
            TargetEntityName = "TestEntity",
            RootNode = new ConstantNode 
            { 
                DataType = Infrastructure.RuleEngine.ValueType.DateTime, 
                Value = "invalid-date" 
            }
        };

        var entity = new TestEntity { Value = 5 };

        // Act & Assert
        var exception = Assert.Throws<RuleExecutionException>(() => _engine.Execute(rule, entity));
        Assert.Contains("Invalid DateTime", exception.Message);
    }

    [Fact]
    public void ValidateRule_InvalidOperatorWithoutOperands_ReturnsInvalid()
    {
        // Arrange - Pravidlo s operátorem bez operandů
        var operationNode = new OperationNode
        {
            Operator = OperatorType.Add,
            Operands = new List<RuleNode>() // Prázdný seznam operandů
        };

        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Invalid Operator",
            TargetEntityName = "TestEntity",
            RootNode = operationNode
        };

        // Act
        var result = _engine.ValidateRule(rule);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains("chyba", result.ErrorMessage?.ToLower());
    }

    [Fact]
    public void Execute_DeepNestedOperations_HandlesCorrectly()
    {
        // Arrange - Velmi vnořené operace: ((1 + 2) * 3) + 4
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Deep Nested",
            TargetEntityName = "TestEntity",
            RootNode = new OperationNode
            {
                Operator = OperatorType.Add,
                Operands = new List<RuleNode>
                {
                    new OperationNode
                    {
                        Operator = OperatorType.Multiply,
                        Operands = new List<RuleNode>
                        {
                            new OperationNode
                            {
                                Operator = OperatorType.Add,
                                Operands = new List<RuleNode>
                                {
                                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Integer, Value = "1" },
                                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Integer, Value = "2" }
                                }
                            },
                            new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Integer, Value = "3" }
                        }
                    },
                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Integer, Value = "4" }
                }
            }
        };

        var entity = new TestEntity { Value = 5 };

        // Act
        var result = _engine.Execute(rule, entity);

        // Assert - ((1 + 2) * 3) + 4 = (3 * 3) + 4 = 9 + 4 = 13
        Assert.Equal(13, result);
    }

    [Fact]
    public void Execute_MixedDataTypes_HandlesConversion()
    {
        // Arrange - Smíchané datové typy: integer + decimal
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Mixed Types",
            TargetEntityName = "TestEntity",
            RootNode = new OperationNode
            {
                Operator = OperatorType.Add,
                Operands = new List<RuleNode>
                {
                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Integer, Value = "10" },
                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "5.5" }
                }
            }
        };

        var entity = new TestEntity { Value = 5 };

        // Act
        var result = _engine.Execute(rule, entity);

        // Assert
        Assert.Equal(15.5m, result);
    }

    [Fact]
    public void IsEntitySupported_EmptyString_ReturnsFalse()
    {
        // Act & Assert
        Assert.False(_engine.IsEntitySupported(""));
        Assert.False(_engine.IsEntitySupported("   "));
        Assert.False(_engine.IsEntitySupported(null!));
    }

    [Fact]
    public void GetSupportedEntities_ReturnsAllRegisteredEntities()
    {
        // Act
        var entities = _engine.GetSupportedEntities().ToList();

        // Assert
        Assert.Contains("TestEntity", entities);
        Assert.Contains("EmptyEntity", entities);
        Assert.Contains("ComplexEntity", entities);
        Assert.Equal(3, entities.Count);
    }

    [Fact]
    public void ClearCache_RemovesAllCachedRules()
    {
        // Arrange - Zkompilujeme nějaká pravidla
        var rule1 = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Rule 1",
            TargetEntityName = "TestEntity",
            RootNode = new SourceValueNode { SourcePath = "Value" }
        };

        var rule2 = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Rule 2",
            TargetEntityName = "TestEntity",
            RootNode = new SourceValueNode { SourcePath = "Value" }
        };

        var entity = new TestEntity { Value = 10 };

        // Zkompilujeme pravidla (přidáme do cache)
        _engine.Execute(rule1, entity);
        _engine.Execute(rule2, entity);

        // Act
        _engine.ClearCache();

        // Assert - Pravidla by měla fungovat i po vymazání cache
        var result1 = _engine.Execute(rule1, entity);
        var result2 = _engine.Execute(rule2, entity);

        Assert.Equal(10, result1);
        Assert.Equal(10, result2);
    }
}

/// <summary>
/// Prázdná entita pro testování edge cases.
/// </summary>
public class EmptyEntity
{
    public Guid Id { get; set; } = Guid.NewGuid();
}

/// <summary>
/// Komplexní entita s různými typy vlastností pro testování.
/// </summary>
public class ComplexEntity
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public TestEntity? NullProperty { get; set; }
    public List<TestEntity> EmptyList { get; set; } = new();
    public string? NullString { get; set; }
    public DateTime? NullableDateTime { get; set; }
    public decimal LargeDecimal { get; set; } = ************.99m;
}
