using Application.Abstraction;
using Application.Features.Generic.Commands;
using Application.Pipeline;
using Application.Abstraction.Mediator;
using Application.Features.Generic;
using Moq;
using SharedKernel.Domain;
using Xunit;
using SharedKernel.Models;

namespace Infrastructure.Tests.Cache;

/// <summary>
/// Testy pro cache invalidation behavior
/// </summary>
public class CacheInvalidationTests
{
    private class TestEntity : BaseEntity<int>
    {
        public string Name { get; set; } = string.Empty;
    }

    private class TestDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
    }

    private class TestCommand : IRequest<Result<bool>>, IInvalidateCache
    {
        public IEnumerable<string> CacheKeys { get; set; } = new[] { "test-key-1", "test-key-2" };
        public IEnumerable<string>? CacheTags { get; set; } = new[] { "TestEntity" };
    }

    [Fact]
    public async Task Handle_ShouldInvalidateCacheKeys()
    {
        // Arrange
        var cacheService = new Mock<ICacheService>();
        var behavior = new CacheInvalidationBehavior<TestCommand, Result<bool>>(cacheService.Object);
        var request = new TestCommand();
        
        var handlerCalled = false;
        RequestHandlerDelegate<Result<bool>> next = () =>
        {
            handlerCalled = true;
            return Task.FromResult(Result<bool>.Ok(true));
        };

        // Act
        var result = await behavior.Handle(request, next, CancellationToken.None);

        // Assert
        Assert.True(handlerCalled);
        Assert.True(result.Succeeded);
        
        // Ověříme, že byly volány správné metody cache service
        cacheService.Verify(c => c.RemoveAsync("test-key-1", It.IsAny<CancellationToken>()), Times.Once);
        cacheService.Verify(c => c.RemoveAsync("test-key-2", It.IsAny<CancellationToken>()), Times.Once);
        cacheService.Verify(c => c.RemoveByTagAsync("TestEntity", It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithEmptyKeys_ShouldNotCallRemoveAsync()
    {
        // Arrange
        var cacheService = new Mock<ICacheService>();
        var behavior = new CacheInvalidationBehavior<TestCommand, Result<bool>>(cacheService.Object);
        var request = new TestCommand
        {
            CacheKeys = new[] { "", "  ", "valid-key" },
            CacheTags = new[] { "", "ValidTag" }
        };
        
        RequestHandlerDelegate<Result<bool>> next = () => Task.FromResult(Result<bool>.Ok(true));

        // Act
        await behavior.Handle(request, next, CancellationToken.None);

        // Assert
        // Prázdné klíče by neměly být volány
        cacheService.Verify(c => c.RemoveAsync("", It.IsAny<CancellationToken>()), Times.Never);
        cacheService.Verify(c => c.RemoveAsync("  ", It.IsAny<CancellationToken>()), Times.Never);
        cacheService.Verify(c => c.RemoveByTagAsync("", It.IsAny<CancellationToken>()), Times.Never);
        
        // Platné klíče by měly být volány
        cacheService.Verify(c => c.RemoveAsync("valid-key", It.IsAny<CancellationToken>()), Times.Once);
        cacheService.Verify(c => c.RemoveByTagAsync("ValidTag", It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public void CreateEntityCommand_ShouldHaveCorrectCacheTags()
    {
        // Arrange
        var command = new CreateEntityCommand<TestEntity, TestDto, int>
        {
            Payload = new TestDto { Name = "Test" }
        };

        // Act & Assert
        Assert.NotNull(command.CacheTags);
        Assert.Contains("TestEntity", command.CacheTags);
        Assert.Empty(command.CacheKeys);
    }

    [Fact]
    public void UpdateEntityCommand_ShouldHaveCorrectCacheTags()
    {
        // Arrange
        var command = new UpdateEntityCommand<TestEntity, TestDto, int>
        {
            Id = 1,
            Payload = new TestDto { Name = "Updated" }
        };

        // Act & Assert
        Assert.NotNull(command.CacheTags);
        Assert.Contains("TestEntity", command.CacheTags);
        Assert.Empty(command.CacheKeys);
    }

    [Fact]
    public void DeleteEntityCommand_ShouldHaveCorrectCacheTags()
    {
        // Arrange
        var command = new DeleteEntityCommand<TestEntity, int>
        {
            Id = 1
        };

        // Act & Assert
        Assert.NotNull(command.CacheTags);
        Assert.Contains("TestEntity", command.CacheTags);
        Assert.Empty(command.CacheKeys);
    }
}
