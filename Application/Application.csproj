<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="FluentValidation" Version="12.0.0" />
      <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.7" />
      <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
      <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.7" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Abstractions" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.Identity.Stores" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.7" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Domain\Domain.csproj" />
    </ItemGroup>

</Project>
