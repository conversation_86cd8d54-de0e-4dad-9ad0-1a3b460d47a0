using Application.Abstraction.Mediator;
using SharedKernel.Models;

namespace Application.Features.Generic.Facade;

/// <summary>
/// Fasáda pro jednoduché použití generických příkazů s automatickým odvozením typů
/// </summary>
public interface IEntityCommandFacade
{
    /// <summary>
    /// Vytvoří novou entitu
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <param name="editDto">DTO pro vytvoření entity</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>ID vytvořené entity</returns>
    Task<Result<object>> CreateAsync<TEntity>(object editDto, CancellationToken cancellationToken = default)
        where TEntity : class;

    /// <summary>
    /// Aktualizuje existující entitu
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <param name="id">ID entity</param>
    /// <param name="editDto">DTO pro aktualizaci entity</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True pokud byla aktualizace úspěšná</returns>
    Task<Result<bool>> UpdateAsync<TEntity>(object id, object editDto, CancellationToken cancellationToken = default)
        where TEntity : class;

    /// <summary>
    /// Smaže entitu podle ID
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <param name="id">ID entity</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True pokud bylo smazání úspěšné</returns>
    Task<Result<bool>> DeleteAsync<TEntity>(object id, CancellationToken cancellationToken = default)
        where TEntity : class;
}

/// <summary>
/// Implementace fasády pro generické příkazy
/// </summary>
public class EntityCommandFacade : IEntityCommandFacade
{
    private readonly IMediator _mediator;
    private readonly IEntityTypeRegistry _typeRegistry;

    public EntityCommandFacade(IMediator mediator, IEntityTypeRegistry typeRegistry)
    {
        _mediator = mediator;
        _typeRegistry = typeRegistry;
    }

    public async Task<Result<object>> CreateAsync<TEntity>(object editDto, CancellationToken cancellationToken = default)
        where TEntity : class
    {
        var typeInfo = _typeRegistry.GetEntityTypeInfo<TEntity>();
        if (typeInfo == null)
        {
            return await Result<object>.ErrorAsync($"Entita {typeof(TEntity).Name} není registrována v EntityTypeRegistry");
        }

        // Vytvoříme generický příkaz pomocí reflection
        var commandType = typeof(Commands.CreateEntityCommand<,,>)
            .MakeGenericType(typeInfo.EntityType, typeInfo.EditDtoType, typeInfo.KeyType);

        var command = Activator.CreateInstance(commandType);
        if (command == null)
        {
            return await Result<object>.ErrorAsync("Nepodařilo se vytvořit CreateEntityCommand");
        }

        // Nastavíme Payload
        var payloadProperty = commandType.GetProperty("Payload");
        payloadProperty?.SetValue(command, editDto);

        // Odešleme příkaz pomocí dynamického volání
        dynamic result = await _mediator.Send((dynamic)command, cancellationToken);

        return await ConvertResultToObject(result);
    }

    public async Task<Result<bool>> UpdateAsync<TEntity>(object id, object editDto, CancellationToken cancellationToken = default)
        where TEntity : class
    {
        var typeInfo = _typeRegistry.GetEntityTypeInfo<TEntity>();
        if (typeInfo == null)
        {
            return await Result<bool>.ErrorAsync($"Entita {typeof(TEntity).Name} není registrována v EntityTypeRegistry");
        }

        // Vytvoříme generický příkaz pomocí reflection
        var commandType = typeof(Commands.UpdateEntityCommand<,,>)
            .MakeGenericType(typeInfo.EntityType, typeInfo.EditDtoType, typeInfo.KeyType);

        var command = Activator.CreateInstance(commandType);
        if (command == null)
        {
            return await Result<bool>.ErrorAsync("Nepodařilo se vytvořit UpdateEntityCommand");
        }

        // Nastavíme Id a Payload
        var idProperty = commandType.GetProperty("Id");
        var payloadProperty = commandType.GetProperty("Payload");
        
        idProperty?.SetValue(command, id);
        payloadProperty?.SetValue(command, editDto);

        // Odešleme příkaz pomocí dynamického volání
        dynamic result = await _mediator.Send((dynamic)command, cancellationToken);

        return await ConvertResultToBool(result);
    }

    public async Task<Result<bool>> DeleteAsync<TEntity>(object id, CancellationToken cancellationToken = default)
        where TEntity : class
    {
        var typeInfo = _typeRegistry.GetEntityTypeInfo<TEntity>();
        if (typeInfo == null)
        {
            return await Result<bool>.ErrorAsync($"Entita {typeof(TEntity).Name} není registrována v EntityTypeRegistry");
        }

        // Vytvoříme generický příkaz pomocí reflection
        var commandType = typeof(Commands.DeleteEntityCommand<,>)
            .MakeGenericType(typeInfo.EntityType, typeInfo.KeyType);

        var command = Activator.CreateInstance(commandType);
        if (command == null)
        {
            return await Result<bool>.ErrorAsync("Nepodařilo se vytvořit DeleteEntityCommand");
        }

        // Nastavíme Id
        var idProperty = commandType.GetProperty("Id");
        idProperty?.SetValue(command, id);

        // Odešleme příkaz pomocí dynamického volání
        dynamic result = await _mediator.Send((dynamic)command, cancellationToken);

        return await ConvertResultToBool(result);
    }

    private static async Task<Result<bool>> ConvertResultToBool(object result)
    {
        // Použijeme reflection pro přístup k vlastnostem Result<bool>
        var succeededProperty = result.GetType().GetProperty("Succeeded");
        var errorsProperty = result.GetType().GetProperty("Errors");
        var dataProperty = result.GetType().GetProperty("Data");

        if (succeededProperty?.GetValue(result) is true)
        {
            var data = dataProperty?.GetValue(result);
            return await Result<bool>.OkAsync(data is bool boolData ? boolData : true);
        }
        else
        {
            var errors = errorsProperty?.GetValue(result) as IEnumerable<string> ?? new[] { "Neznámá chyba" };
            return await Result<bool>.ErrorAsync(errors.ToArray());
        }
    }

    private static async Task<Result<object>> ConvertResultToObject(object result)
    {
        // Použijeme reflection pro přístup k vlastnostem Result<T>
        var succeededProperty = result.GetType().GetProperty("Succeeded");
        var errorsProperty = result.GetType().GetProperty("Errors");
        var dataProperty = result.GetType().GetProperty("Data");

        if (succeededProperty?.GetValue(result) is true)
        {
            var data = dataProperty?.GetValue(result);
            return await Result<object>.OkAsync(data);
        }
        else
        {
            var errors = errorsProperty?.GetValue(result) as IEnumerable<string> ?? new[] { "Neznámá chyba" };
            return await Result<object>.ErrorAsync(errors.ToArray());
        }
    }
}
