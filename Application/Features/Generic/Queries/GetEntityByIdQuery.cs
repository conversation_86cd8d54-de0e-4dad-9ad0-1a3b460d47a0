using Application.Abstraction;
using Application.Abstraction.Mediator;
using Application.Exceptions;
using Application.Features.Generic;
using Application.Models;
using Application.Services;
using SharedKernel.Models;


namespace Application.Features.Generic.Queries;

/// <summary>
/// Generický dotaz pro získání entity podle ID s volitelnou podporou cache
/// </summary>
/// <typeparam name="TDto">Typ DTO objektu</typeparam>
public class GetEntityByIdQuery<TDto> : IRequest<Result<TDto>>, ICachableQuery<Result<TDto>>
{
    /// <summary>
    /// Název entity pro cache tagy - musí být nastaven handlerem
    /// </summary>
    public string? EntityName { get; set; }
    /// <summary>
    /// ID entity, která má být načtena
    /// </summary>
    public required int Id { get; set; }

    /// <summary>
    /// Určuje, zda má být použita cache pro tento dotaz
    /// </summary>
    public bool UseCache { get; set; } = false;

    /// <summary>
    /// Klíč pro cache - obsahuje typ DTO a ID entity
    /// </summary>
    public string CacheKey => UseCache ? $"GetById_{typeof(TDto).Name}_{Id}" : string.Empty;

    /// <summary>
    /// Tagy pro invalidaci cache
    /// </summary>
    public IEnumerable<string>? Tags => UseCache && !string.IsNullOrEmpty(EntityName) ? new[] { EntityName } : null;
}

internal class GetEntityByIdQueryHandler<TEntity, TDto>
    : GenericSingleQueryHandler<TEntity, TDto, GetEntityByIdQuery<TDto>>
    where TEntity : class
    where TDto : class, new()
{
    public GetEntityByIdQueryHandler(IApplicationDbContext context, IUnifiedMapper<TEntity, TDto> mapper)
        : base(context, mapper)
    {
    }

    protected override async Task<TDto> FetchSingleAsync(GetEntityByIdQuery<TDto> request,
        CancellationToken cancellationToken)
    {
        var entity = await Context.Set<TEntity>()
            .FindAsync([request.Id], cancellationToken);
        if (entity is null)
            throw new NotFoundException(typeof(TEntity).Name, request.Id);

        return Mapper.Map(entity, useConfig: false);
    }
}
