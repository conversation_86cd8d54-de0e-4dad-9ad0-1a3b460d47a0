using Application.Abstraction;
using Application.Abstraction.Mediator;
using Application.Models;
using System.Text;
using SharedKernel.Models;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.Generic.Queries;

/// <summary>
/// Základní dotaz pro stránkovací dotazy entit s volitelnou podporou cache a specifikací
/// </summary>
/// <typeparam name="TDto">Typ DTO pro výstup</typeparam>
/// <typeparam name="TEntity">Typ entity</typeparam>
public class GetPagedEntitiesQuery<TDto, TEntity> : IRequest<PagedResult<TDto>>, ICachableQuery<PagedResult<TDto>>
    where TEntity : class
{
    /// <summary>
    /// Název entity pro cache tagy - musí být nastaven handlerem
    /// </summary>
    public string? EntityName { get; set; }
    private int _pageNumber = 1;
    private int _pageSize = 10;

    /// <summary>
    /// <PERSON><PERSON><PERSON> str<PERSON> (1 a více)
    /// </summary>
    public int PageNumber
    {
        get => _pageNumber;
        set => _pageNumber = value < 1 ? 1 : value;
    }

    /// <summary>
    /// Velikost stránky (1 až 100)
    /// </summary>
    public int PageSize
    {
        get => _pageSize;
        set => _pageSize = value switch
        {
            < 1 => 10,
            > 100 => 100,
            _ => value
        };
    }

    /// <summary>
    /// Název vlastnosti podle které se má řadit
    /// </summary>
    public string? SortBy { get; set; }

    /// <summary>
    /// Příznak pro sestupné řazení
    /// </summary>
    public bool SortDescending { get; set; } = false;

    /// <summary>
    /// Určuje, zda má být použita cache pro tento dotaz
    /// </summary>
    public bool UseCache { get; set; } = false;

    /// <summary>
    /// Specifikace pro filtrování a eager loading
    /// </summary>
    public ISpecification<TEntity>? Specification { get; set; }
    
    /// <summary>
    /// Implementace ICachableQuery - klíč pro cache
    /// </summary>
    public virtual string CacheKey
    {
        get
        {
            if (!UseCache)
                return string.Empty;

            var key = new StringBuilder($"GetPaged_{typeof(TDto).Name}");
            key.Append($"_Page{PageNumber}");
            key.Append($"_Size{PageSize}");

            if (!string.IsNullOrEmpty(SortBy))
            {
                key.Append($"_Sort{SortBy}");
                if (SortDescending)
                    key.Append("Desc");
            }

            // Pokud je specifikace definována, přidáme její hash do klíče
            if (Specification != null)
            {
                var specHash = GetSpecificationHash(Specification);
                key.Append($"_Spec{specHash}");
            }

            return key.ToString();
        }
    }

    /// <summary>
    /// Implementace ICachableQuery - tagy pro invalidaci cache
    /// </summary>
    public virtual IEnumerable<string>? Tags => UseCache && !string.IsNullOrEmpty(EntityName) ? new[] { EntityName } : null;

    /// <summary>
    /// Vytvoří hash ze specifikace pro cache klíč
    /// </summary>
    private static string GetSpecificationHash(ISpecification<TEntity> specification)
    {
        // Jednoduchý hash založený na typu specifikace
        // V produkčním prostředí by bylo lepší použít složitější hash zahrnující kritéria
        return specification.GetType().Name.GetHashCode().ToString("X");
    }
}



/// <summary>
/// Obecný handler pro zpracování stránkovacích dotazů se specifikací
/// </summary>
public class GetPagedEntitiesQueryHandler<TEntity, TDto>
    : IRequestHandler<GetPagedEntitiesQuery<TDto, TEntity>, PagedResult<TDto>>
    where TEntity : class
    where TDto : class, new()
{
    protected readonly IApplicationDbContext Context;
    protected readonly IUnifiedMapper<TEntity, TDto> Mapper;

    public GetPagedEntitiesQueryHandler(IApplicationDbContext context, IUnifiedMapper<TEntity, TDto> mapper)
    {
        Context = context;
        Mapper = mapper;
    }

    public async Task<PagedResult<TDto>> Handle(GetPagedEntitiesQuery<TDto, TEntity> request, CancellationToken cancellationToken)
    {
        try
        {
            // Nastavíme EntityName pro cache tagy
            request.EntityName = typeof(TEntity).Name;

            var query = Context.Set<TEntity>().AsQueryable();

        // Aplikace specifikace pokud je definována
        if (request.Specification != null)
        {
            // Pro stránkování musíme aplikovat specifikaci bez stránkování pro počítání
            var countQuery = SpecificationEvaluator<TEntity>.GetQueryForCount(query, request.Specification);
            var totalCount = await countQuery.CountAsync(cancellationToken);

            // Aplikace kompletní specifikace včetně stránkování
            query = SpecificationEvaluator<TEntity>.GetQuery(query, request.Specification);

            // Pokud specifikace obsahuje stránkování, použijeme ho
            if (request.Specification.IsPagingEnabled)
            {
                var items = await query.ToListAsync(cancellationToken);
                var mappedItems = Mapper.MapCollection(items, useConfig: false).ToList();

                var currentPage = (request.Specification.Skip / request.Specification.Take) + 1;

                // Vytvoříme PagedResult pomocí synchronní metody Create
                var dummyQuery = mappedItems.AsQueryable();
                return PagedResult<TDto>.Create(dummyQuery, currentPage, request.Specification.Take, (Func<TDto, TDto>)(x => x));
            }
        }

        // Standardní zpracování bez specifikace nebo se specifikací bez stránkování
        // Aplikace filtrů - přetížit v konkrétních implementacích
        query = ApplyFiltersWithSpec(query, request);

            // Aplikace řazení
            try
            {
                query = ApplySortingWithSpec(query, request);
            }
            catch (Exception ex)
            {
                return PagedResult<TDto>.Error($"Chyba při řazení: {ex.Message}");
            }

        // Aplikace eager loadingu - přetížit v konkrétních implementacích
        query = ApplyIncludesWithSpec(query, request);

            var pagedList =  PagedResult<TDto>.Create(
                query,
                request.PageNumber,
                request.PageSize,
                (Func<TEntity, TDto>)(entity => Mapper.Map(entity, useConfig: false)));

            return pagedList;
        }
        catch (Exception ex)
        {
            return PagedResult<TDto>.Error($"Chyba při získávání stránkovaných dat: {ex.Message}");
        }
    }

    /// <summary>
    /// Aplikuje filtry na dotaz se specifikací. Výchozí implementace neaplikuje žádné filtry.
    /// Přetižte tuto metodu v odvozené třídě pro implementaci specifických filtrů.
    /// </summary>
    protected virtual IQueryable<TEntity> ApplyFiltersWithSpec(
        IQueryable<TEntity> query,
        GetPagedEntitiesQuery<TDto, TEntity> request)
    {
        return query;
    }

    /// <summary>
    /// Aplikuje řazení na dotaz se specifikací podle zadaného názvu vlastnosti
    /// </summary>
    protected virtual IQueryable<TEntity> ApplySortingWithSpec(
        IQueryable<TEntity> query,
        GetPagedEntitiesQuery<TDto, TEntity> request)
    {
        // Pokud není zadán název vlastnosti pro řazení, vrátíme dotaz beze změny
        if (string.IsNullOrEmpty(request.SortBy))
            return query;

        // Zjistíme, zda entita obsahuje vlastnost s daným názvem
        var propertyInfo = typeof(TEntity).GetProperty(request.SortBy);
        if (propertyInfo == null)
            throw new InvalidOperationException($"Vlastnost '{request.SortBy}' nebyla nalezena na entitě {typeof(TEntity).Name}");

        // Vytvoříme lambda výraz pro řazení
        var parameter = System.Linq.Expressions.Expression.Parameter(typeof(TEntity), "x");
        var property = System.Linq.Expressions.Expression.Property(parameter, propertyInfo);
        var lambda = System.Linq.Expressions.Expression.Lambda(property, parameter);

        // Aplikujeme řazení (vzestupné nebo sestupné)
        var methodName = request.SortDescending ? "OrderByDescending" : "OrderBy";
        var resultExp = System.Linq.Expressions.Expression.Call(
            typeof(Queryable),
            methodName,
            new[] { typeof(TEntity), propertyInfo.PropertyType },
            query.Expression,
            System.Linq.Expressions.Expression.Quote(lambda));

        return query.Provider.CreateQuery<TEntity>(resultExp);
    }

    /// <summary>
    /// Aplikuje eager loading na dotaz se specifikací. Výchozí implementace nepřidává žádné include.
    /// Přetižte tuto metodu v odvozené třídě pro načtení souvisejících entit.
    /// </summary>
    protected virtual IQueryable<TEntity> ApplyIncludesWithSpec(
        IQueryable<TEntity> query,
        GetPagedEntitiesQuery<TDto, TEntity> request)
    {
        return query;
    }
    
    /// <summary>
    /// Aplikuje řazení na dotaz podle zadaného názvu vlastnosti
    /// </summary>
    protected virtual IQueryable<TEntity> ApplySorting(
        IQueryable<TEntity> query,
        GetPagedEntitiesQuery<TDto, TEntity> request)
    {
        // Pokud není zadán název vlastnosti pro řazení, vrátíme dotaz beze změny
        if (string.IsNullOrEmpty(request.SortBy))
            return query;

        // Zjistíme, zda entita obsahuje vlastnost s daným názvem
        var propertyInfo = typeof(TEntity).GetProperty(request.SortBy);
        if (propertyInfo == null)
            throw new InvalidOperationException($"Vlastnost '{request.SortBy}' nebyla nalezena na entitě {typeof(TEntity).Name}");

        // Vytvoříme lambda výraz pro řazení
        var parameter = System.Linq.Expressions.Expression.Parameter(typeof(TEntity), "x");
        var property = System.Linq.Expressions.Expression.Property(parameter, propertyInfo);
        var lambda = System.Linq.Expressions.Expression.Lambda(property, parameter);

        // Aplikujeme řazení (vzestupné nebo sestupné)
        var methodName = request.SortDescending ? "OrderByDescending" : "OrderBy";
        var resultExp = System.Linq.Expressions.Expression.Call(
            typeof(Queryable),
            methodName,
            new[] { typeof(TEntity), propertyInfo.PropertyType },
            query.Expression,
            System.Linq.Expressions.Expression.Quote(lambda));

        return query.Provider.CreateQuery<TEntity>(resultExp);
    }
    
    /// <summary>
    /// Aplikuje eager loading na dotaz. Výchozí implementace nepřidává žádné include.
    /// Přetižte tuto metodu v odvozené třídě pro načtení souvisejících entit.
    /// </summary>
    protected virtual IQueryable<TEntity> ApplyIncludes(
        IQueryable<TEntity> query,
        GetPagedEntitiesQuery<TDto, TEntity> request)
    {
        return query;
    }
}


