using Application.Abstraction.Mediator;
using Application.Features.Generic.Queries;
using Application.Features.Sample;
using Domain.Entities;
using SharedKernel.Models;

namespace Application.Features.Generic.Specifications;

/// <summary>
/// Ukázkové použití specifikací v aplikaci
/// </summary>
public class SpecificationUsageExamples
{
    private readonly IMediator _mediator;

    public SpecificationUsageExamples(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Příklad použití specifikace pro získání aktivních sample entit
    /// </summary>
    public async Task<List<SampleDto>> GetActiveSamplesAsync()
    {
        var query = new GetAllEntitiesQuery<SampleDto, SampleEntity>
        {
            Specification = new SampleSpecifications.ActiveSamplesSpecification(),
            UseCache = true
        };

        var result = await _mediator.Send(query);
        return result.Data ?? new List<SampleDto>();
    }

    /// <summary>
    /// Příklad použití specifikace pro vyhledání podle názvu
    /// </summary>
    public async Task<List<SampleDto>> SearchSamplesByNameAsync(string nameFilter)
    {
        var query = new GetAllEntitiesQuery<SampleDto, SampleEntity>
        {
            Specification = new SampleSpecifications.SamplesByNameSpecification(nameFilter),
            UseCache = false // Vyhledávání obvykle neukládáme do cache
        };

        var result = await _mediator.Send(query);
        return result.Data ?? new List<SampleDto>();
    }

    /// <summary>
    /// Příklad použití specifikace pro stránkované výsledky
    /// </summary>
    public async Task<PagedResult<SampleDto>> GetPagedActiveSamplesAsync(int pageNumber, int pageSize)
    {
        var query = new GetPagedEntitiesQuery<SampleDto, SampleEntity>
        {
            Specification = new SampleSpecifications.PagedActiveSamplesSpecification(pageNumber, pageSize),
            UseCache = true
        };

        var result = await _mediator.Send(query);
        return result;
    }

    /// <summary>
    /// Příklad použití komplexní specifikace s více kritérii
    /// </summary>
    public async Task<Result<List<SampleDto>>> GetSamplesWithComplexFilterAsync(
        string? nameFilter = null,
        bool? isActive = null,
        DateTime? createdAfter = null)
    {
        var query = new GetAllEntitiesQuery<SampleDto, SampleEntity>
        {
            Specification = new SampleSpecifications.ComplexSampleSpecification(
                nameFilter, isActive, createdAfter),
            UseCache = false
        };

        var result = await _mediator.Send(query);
        return result;
    }

    /// <summary>
    /// Příklad použití obecné specifikace pro entity vytvořené po určitém datu
    /// </summary>
    public async Task<Result<List<SampleDto>>> GetRecentSamplesAsync(DateTime createdAfter)
    {
        var query = new GetAllEntitiesQuery<SampleDto, SampleEntity>
        {
            Specification = new CommonSpecifications.CreatedAfterSpecification<SampleEntity>(createdAfter),
            UseCache = true
        };

        var result = await _mediator.Send(query);
        return result;
    }

    /// <summary>
    /// Příklad kombinace standardního stránkování s filtrováním pomocí specifikace
    /// </summary>
    public async Task<PagedResult<SampleDto>> GetFilteredPagedSamplesAsync(
        string nameFilter, int pageNumber, int pageSize)
    {
        var query = new GetPagedEntitiesQuery<SampleDto, SampleEntity>
        {
            // Použijeme specifikaci pouze pro filtrování, stránkování necháme na standardní logice
            Specification = new SampleSpecifications.SamplesByNameSpecification(nameFilter),
            PageNumber = pageNumber,
            PageSize = pageSize,
            UseCache = false
        };

        var result = await _mediator.Send(query);
        return result;
    }

    /// <summary>
    /// Příklad použití specifikace s eager loading (pokud by bylo potřeba)
    /// </summary>
    public async Task<List<SampleDto>> GetSamplesWithRelatedDataAsync()
    {
        // Toto je ukázka - SampleEntity zatím nemá related entities
        // ale ukážeme, jak by se to dělalo pomocí vlastní specifikace
        var specification = new SampleSpecifications.ActiveSamplesSpecification();
        // V reálné implementaci by specifikace obsahovala:
        // specification.AddInclude(x => x.RelatedEntity);
        // specification.AddInclude(x => x.AnotherRelatedEntity);

        var query = new GetAllEntitiesQuery<SampleDto, SampleEntity>
        {
            Specification = specification,
            UseCache = true
        };

        var result = await _mediator.Send(query);
        return result.Data ?? new List<SampleDto>();
    }
}
