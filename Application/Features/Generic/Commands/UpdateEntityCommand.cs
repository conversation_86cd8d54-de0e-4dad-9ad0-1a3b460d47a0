using Application.Abstraction;
using Application.Abstraction.Mediator;
using Application.Exceptions;
using SharedKernel.Domain;
using SharedKernel.Models;

namespace Application.Features.Generic.Commands;

// 1.1) Command
public class UpdateEntityCommand<TEntity, TEditDto, TKey> : IRequest<Result<bool>>, IInvalidateCache
    where TEditDto : class, new()
{
    public required TKey Id { get; init; }
    public required TEditDto Payload { get; init; }

    /// <summary>
    /// Cache klíče pro invalidaci cache
    /// </summary>
    public IEnumerable<string> CacheKeys { get; } = Array.Empty<string>();

    /// <summary>
    /// Cache tagy pro invalidaci cache - vymaže všechno pro tuhle entitu
    /// </summary>
    public IEnumerable<string>? CacheTags => new[] { typeof(TEntity).Name };
}


// 1.2) Handler
public class UpdateEntityCommandHandler<TEntity, TEditDto, TK<PERSON>>
    : IRequestHandler<UpdateEntityCommand<TEntity,TEditDto, TKey>, Result<bool>>
    where TEntity : BaseEntity<TKey>
    where TEditDto : class, new()
{
    private readonly IApplicationDbContext _ctx;
    private readonly IUnifiedMapper<TEditDto, TEntity> _mapper;

    public UpdateEntityCommandHandler(
        IApplicationDbContext ctx,
        IUnifiedMapper<TEditDto, TEntity> mapper)
    {
        _ctx = ctx;
        _mapper = mapper;
    }

    public async Task<Result<bool>> Handle(
        UpdateEntityCommand<TEntity, TEditDto, TKey> request,
        CancellationToken ct)
    {
        // Najít existující entitu
        var entity = await _ctx.Set<TEntity>()
            .FindAsync(new object[] { request.Id! }, ct);

        if (entity is null)
            return Result<bool>.Error("NotFound", $"{typeof(TEntity).Name} s ID {request.Id} nebyl nalezen.");

        // 1) Map payload to entity
        // Map payload → entita
        _mapper.Update(request.Payload, entity);

        // 2) Ulož změny

        try {
            int writtenEntriesCount = await _ctx.SaveChangesAsync(ct);
            if (writtenEntriesCount > 0)
            {
                return await Result<bool>.OkAsync(true);
            }
            else
            {
                return await Result<bool>.OkAsync(false);
            }
        }
        catch(Exception ex)
        {
            return await Result<bool>.ErrorAsync(ex.Message);
        }
    }
}


