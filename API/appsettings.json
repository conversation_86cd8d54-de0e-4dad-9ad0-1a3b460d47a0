{"ConnectionStrings": {"DefaultConnection": "Data Source=../Data/datacapture.db"}, "AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "qualified.domain.name", "TenantId": "22222222-2222-2222-2222-222222222222", "ClientId": "11111111-1111-1111-11111111111111111", "Scopes": "access_as_user", "CallbackPath": "/signin-oidc"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}