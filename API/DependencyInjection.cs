using System.Text.Json.Serialization;
using API.ApiService;
using API.Endpoints;
using API.Filters;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Infrastructure;

namespace API;

/// <summary>
/// Registrace služeb a konfigurace specifické pro API vrstvu
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// Registruje služby specifické pro API vrstvu
    /// </summary>
    /// <param name="services"><PERSON>lek<PERSON> služeb</param>
    /// <returns>Kolekce služeb pro fluent API</returns>
    public static IServiceCollection AddApiServices(this IServiceCollection services)
    {
        // Registrace API exploreru a OpenAPI
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen(c =>
        {
            // Všechny vlastnosti budou považovány za povinné, pokud nejsou explicitně označeny jako voliteln<PERSON>
            c.SchemaFilter<RequiredNotNullableSchemaFilter>();
            // EF Core validační pravidla do Swagger schématu
            c.SchemaFilter<EfCoreSchemaFilter>();
        });
        services.AddOpenApi(); // Microsoft.AspNetCore.OpenApi

        // Konfigurace kontrolerů a JSON serializace
        services.AddControllers()
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
            });

        // Registrace API služeb
        services.AddScoped(typeof(ICrudApiService<>), typeof(CrudApiService<>));

        return services;
    }

    /// <summary>
    /// Konfiguruje API endpointy a middleware
    /// </summary>
    /// <param name="app">WebApplication instance</param>
    /// <returns>WebApplication pro fluent API</returns>
    public static WebApplication UseApiConfiguration(this WebApplication app)
    {
        // Swagger konfigurace
        app.UseSwagger();
        app.UseSwaggerUI(options =>
        {
            options.SwaggerEndpoint("/swagger/v1/swagger.json", "My API V1");
            // případně options.RoutePrefix = string.Empty;  // UI na /
        });

        // Redirect root na Swagger UI (volitelné)
        app.MapGet("/", () => Results.Redirect("/swagger")).ExcludeFromDescription();

        // === RuleEngine API (registrováno z Infrastructure vrstvy) ===
        app.UseRuleEngineEndpoints();

        // === Automatická registrace všech CRUD endpointů ===
        app.MapAllEntityEndpoints(includeSpecifications: true);

        return app;
    }
}
