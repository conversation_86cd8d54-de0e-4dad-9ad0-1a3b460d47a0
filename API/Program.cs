using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Identity.Web;
using API;
using Application;
using Infrastructure;

var builder = WebApplication.CreateBuilder(args);

// === Registrace služeb ===
// API vrstva - endpointy, swagger, JSON konfigurace
builder.Services.AddApiServices();

// Autentifikace a autorizace (globální pro celou aplikaciju)
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddMicrosoftIdentityWebApi(builder.Configuration, "AzureAd");
builder.Services.AddAuthorization();

// Ostatní vrstvy
builder.Services.AddApplicationServices();
builder.Services.AddInfrastructureServices(builder.Configuration);

var app = builder.Build();

// === Konfigurace middleware pipeline ===
// HTTPS redirection (globální bezpečnost)
app.UseHttpsRedirection();

// Autentifikace a autorizace (globální pro celou aplikaci)
app.UseAuthentication();
app.UseAuthorization();

// API konfigurace (endpointy, swagger, atd.)
app.UseApiConfiguration();

app.Run();


// Umožňuje přístup k Program třídě pro testování
public partial class Program { }
