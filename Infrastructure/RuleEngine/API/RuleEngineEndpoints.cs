using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;

namespace Infrastructure.RuleEngine.API;

/// <summary>
/// Minimal API endpointy pro RuleEngine systém.
/// Poskytuje technické rozhraní pro vytváření, úpravu a testování pravidel.
/// </summary>
public static class RuleEngineEndpoints
{
    /// <summary>
    /// Registruje všechny RuleEngine endpointy.
    /// </summary>
    public static IEndpointRouteBuilder MapRuleEngineEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/rule-engine")
            .WithTags("RuleEngine");

        // GET /api/rule-engine/rules - Získá všechna pravidla
        group.MapGet("/rules", async (
            [FromServices] IRuleRepository repository) =>
        {
            try
            {
                var rules = await repository.GetAllAsync();
                return Results.Ok(rules);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při načítání pravidel: {ex.Message}");
            }
        })
        .WithName("GetAllRules")
        .WithSummary("Získá všechna obchodní pravidla")
        .WithDescription("Vrací seznam všech obchodních pravidel v systému")
        .Produces<IEnumerable<BusinessRule>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status500InternalServerError);

        // GET /api/rule-engine/rules/{id} - Získá konkrétní pravidlo
        group.MapGet("/rules/{id:guid}", async (
            Guid id,
            [FromServices] IRuleRepository repository) =>
        {
            try
            {
                var rule = await repository.GetByIdAsync(id);
                if (rule == null)
                {
                    return Results.NotFound($"Pravidlo s ID {id} nebylo nalezeno.");
                }
                return Results.Ok(rule);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při načítání pravidla: {ex.Message}");
            }
        })
        .WithName("GetRule")
        .WithSummary("Získá obchodní pravidlo podle ID")
        .WithDescription("Vrací konkrétní obchodní pravidlo včetně jeho struktury")
        .Produces<BusinessRule>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status500InternalServerError);

        // POST /api/rule-engine/rules - Vytvoří nové pravidlo
        group.MapPost("/rules", async (
            [FromBody] BusinessRule rule,
            [FromServices] IRuleRepository repository,
            [FromServices] CalculationEngine engine) =>
        {
            try
            {
                // Kontrola duplicitního názvu
                if (await repository.ExistsWithNameAsync(rule.Name, null))
                {
                    return Results.BadRequest($"Pravidlo s názvem '{rule.Name}' již existuje.");
                }

                // Nastavení základních vlastností
                rule.Id = Guid.NewGuid();
                rule.SchemaVersion = "1.0";

                // Validace syntaxe
                var validationResult = engine.ValidateRule(rule);
                if (!validationResult.IsValid)
                {
                    return Results.BadRequest(new {
                        Message = "Pravidlo obsahuje syntaktické chyby.",
                        Errors = new[] { validationResult.ErrorMessage }
                    });
                }

                await repository.AddAsync(rule);
                return Results.CreatedAtRoute("GetRule", new { id = rule.Id }, rule);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při vytváření pravidla: {ex.Message}");
            }
        })
        .WithName("CreateRule")
        .WithSummary("Vytvoří nové obchodní pravidlo")
        .WithDescription("Vytvoří nové obchodní pravidlo s validací syntaxe")
        .Accepts<BusinessRule>("application/json")
        .Produces<BusinessRule>(StatusCodes.Status201Created)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status500InternalServerError);

        // PUT /api/rule-engine/rules/{id} - Aktualizuje pravidlo
        group.MapPut("/rules/{id:guid}", async (
            Guid id,
            [FromBody] BusinessRule updatedRule,
            [FromServices] IRuleRepository repository,
            [FromServices] CalculationEngine engine) =>
        {
            try
            {
                var existingRule = await repository.GetByIdAsync(id);
                if (existingRule == null)
                {
                    return Results.NotFound($"Pravidlo s ID {id} nebylo nalezeno.");
                }

                // Kontrola duplicitního názvu (kromě aktuálního pravidla)
                if (await repository.ExistsWithNameAsync(updatedRule.Name, id))
                {
                    return Results.BadRequest($"Pravidlo s názvem '{updatedRule.Name}' již existuje.");
                }

                // Aktualizace pravidla (zachování ID a RowVersion)
                existingRule.Name = updatedRule.Name;
                existingRule.Description = updatedRule.Description;
                existingRule.TargetEntityName = updatedRule.TargetEntityName;
                existingRule.TargetProperty = updatedRule.TargetProperty;
                existingRule.RootNode = updatedRule.RootNode;
                existingRule.IsActive = updatedRule.IsActive;
                existingRule.InternalNotes = updatedRule.InternalNotes;

                // Validace syntaxe
                var validationResult = engine.ValidateRule(existingRule);
                if (!validationResult.IsValid)
                {
                    return Results.BadRequest(new {
                        Message = "Pravidlo obsahuje syntaktické chyby.",
                        Errors = new[] { validationResult.ErrorMessage }
                    });
                }

                await repository.UpdateAsync(existingRule);

                // Vymazání z cache
                engine.InvalidateRule(id);

                return Results.NoContent();
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při aktualizaci pravidla: {ex.Message}");
            }
        })
        .WithName("UpdateRule")
        .WithSummary("Aktualizuje obchodní pravidlo")
        .WithDescription("Aktualizuje existující obchodní pravidlo s validací syntaxe")
        .Accepts<BusinessRule>("application/json")
        .Produces(StatusCodes.Status204NoContent)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status500InternalServerError);

        // DELETE /api/rule-engine/rules/{id} - Smaže pravidlo
        group.MapDelete("/rules/{id:guid}", async (
            Guid id,
            [FromServices] IRuleRepository repository,
            [FromServices] CalculationEngine engine) =>
        {
            try
            {
                var rule = await repository.GetByIdAsync(id);
                if (rule == null)
                {
                    return Results.NotFound($"Pravidlo s ID {id} nebylo nalezeno.");
                }

                await repository.DeleteAsync(id);
                
                // Vymazání z cache
                engine.InvalidateRule(id);
                
                return Results.NoContent();
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při mazání pravidla: {ex.Message}");
            }
        })
        .WithName("DeleteRule")
        .WithSummary("Smaže obchodní pravidlo")
        .WithDescription("Smaže existující obchodní pravidlo ze systému")
        .Produces(StatusCodes.Status204NoContent)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status500InternalServerError);

        // GET /api/rule-engine/metadata - Získá metadata pro tvorbu pravidel
        group.MapGet("/metadata", (
            [FromServices] CalculationEngine engine) =>
        {
            try
            {
                var metadata = new RuleMetadata
                {
                    AvailableEntities = GetAvailableEntities(),
                    AvailableOperators = GetAvailableOperators(),
                    AvailableAggregations = GetAvailableAggregations(),
                    AvailableValueTypes = GetAvailableValueTypes(),
                    SchemaVersion = "1.0"
                };
                return Results.Ok(metadata);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při načítání metadat: {ex.Message}");
            }
        })
        .WithName("GetMetadata")
        .WithSummary("Získá metadata pro tvorbu pravidel")
        .WithDescription("Vrací dostupné entity, operátory a další metadata potřebná pro vytváření pravidel")
        .Produces<RuleMetadata>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status500InternalServerError);

        // GET /api/rule-engine/entities/{entityName}/properties - Získá vlastnosti entity
        group.MapGet("/entities/{entityName}/properties", (
            string entityName,
            [FromServices] CalculationEngine engine) =>
        {
            try
            {
                if (!engine.IsEntitySupported(entityName))
                {
                    return Results.NotFound($"Entita '{entityName}' není podporována.");
                }

                var properties = GetEntityPropertiesInternal(entityName);
                return Results.Ok(properties);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při načítání vlastností entity: {ex.Message}");
            }
        })
        .WithName("GetEntityProperties")
        .WithSummary("Získá vlastnosti konkrétní entity")
        .WithDescription("Vrací seznam vlastností zadané entity včetně jejich typů a popisů")
        .Produces<IEnumerable<PropertyMetadata>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status500InternalServerError);

        return app;
    }

    // Helper metody pro metadata
    private static List<EntityMetadata> GetAvailableEntities()
    {
        return new List<EntityMetadata>
        {
            new() { Name = "SampleEntity", DisplayName = "Ukázková entita", Description = "Entita pro demonstraci funkcionality" },
            new() { Name = "BusinessRule", DisplayName = "Obchodní pravidlo", Description = "Technická entita pro správu pravidel" },
            new() { Name = "AuditTrail", DisplayName = "Audit záznam", Description = "Záznam o změnách v systému" },
            new() { Name = "Order", DisplayName = "Objednávka", Description = "Objednávka zákazníka s položkami a dodacími údaji" },
            new() { Name = "OrderItem", DisplayName = "Položka objednávky", Description = "Jednotlivá položka v objednávce" },
            new() { Name = "Invoice", DisplayName = "Faktura", Description = "Faktura vystavená zákazníkovi" },
            new() { Name = "InvoiceItem", DisplayName = "Položka faktury", Description = "Jednotlivá položka na faktuře" }
        };
    }

    private static List<OperatorMetadata> GetAvailableOperators()
    {
        return new List<OperatorMetadata>
        {
            new() { Value = "Add", DisplayName = "Sčítání (+)", Category = "Aritmetické", Description = "Sečte dva číselné operandy" },
            new() { Value = "Subtract", DisplayName = "Odčítání (-)", Category = "Aritmetické", Description = "Odečte druhý operand od prvního" },
            new() { Value = "Multiply", DisplayName = "Násobení (*)", Category = "Aritmetické", Description = "Vynásobí dva číselné operandy" },
            new() { Value = "Divide", DisplayName = "Dělení (/)", Category = "Aritmetické", Description = "Vydělí první operand druhým" },
            new() { Value = "Equal", DisplayName = "Rovná se (==)", Category = "Porovnání", Description = "Porovná dva operandy na rovnost" },
            new() { Value = "NotEqual", DisplayName = "Nerovná se (!=)", Category = "Porovnání", Description = "Porovná dva operandy na nerovnost" },
            new() { Value = "GreaterThan", DisplayName = "Větší než (>)", Category = "Porovnání", Description = "Ověří, zda je první operand větší než druhý" },
            new() { Value = "LessThan", DisplayName = "Menší než (<)", Category = "Porovnání", Description = "Ověří, zda je první operand menší než druhý" },
            new() { Value = "And", DisplayName = "Logické AND (&&)", Category = "Logické", Description = "Logický součin dvou boolean operandů" },
            new() { Value = "Or", DisplayName = "Logické OR (||)", Category = "Logické", Description = "Logický součet dvou boolean operandů" },
            new() { Value = "Not", DisplayName = "Logické NOT (!)", Category = "Logické", Description = "Logická negace boolean operandu" },
            new() { Value = "If", DisplayName = "Podmínka (IF-THEN-ELSE)", Category = "Podmíněné", Description = "Podmíněné vyhodnocení: IF podmínka THEN hodnota1 ELSE hodnota2" }
        };
    }

    private static List<AggregationMetadata> GetAvailableAggregations()
    {
        return new List<AggregationMetadata>
        {
            new() { Value = "Sum", DisplayName = "Součet", Description = "Sečte hodnoty zadaného pole", RequiresField = true },
            new() { Value = "Count", DisplayName = "Počet", Description = "Spočítá počet záznamů", RequiresField = false },
            new() { Value = "Average", DisplayName = "Průměr", Description = "Vypočítá průměr hodnot zadaného pole", RequiresField = true },
            new() { Value = "Min", DisplayName = "Minimum", Description = "Najde nejmenší hodnotu zadaného pole", RequiresField = true },
            new() { Value = "Max", DisplayName = "Maximum", Description = "Najde největší hodnotu zadaného pole", RequiresField = true }
        };
    }

    private static List<ValueTypeMetadata> GetAvailableValueTypes()
    {
        return new List<ValueTypeMetadata>
        {
            new() { Value = "String", DisplayName = "Text", Description = "Textová hodnota (např. \"Hello World\")" },
            new() { Value = "Integer", DisplayName = "Celé číslo", Description = "Celé číslo (např. 42, -10)" },
            new() { Value = "Decimal", DisplayName = "Desetinné číslo", Description = "Desetinné číslo (např. 3.14, -2.5)" },
            new() { Value = "Boolean", DisplayName = "Pravdivostní hodnota", Description = "Pravda nebo nepravda (true/false)" },
            new() { Value = "DateTime", DisplayName = "Datum a čas", Description = "Datum a čas (např. 2024-01-15 10:30:00)" },
            new() { Value = "Guid", DisplayName = "Jedinečný identifikátor", Description = "GUID identifikátor (např. 123e4567-e89b-12d3-a456-************)" }
        };
    }

    private static List<PropertyMetadata> GetEntityPropertiesInternal(string entityName)
    {
        return entityName switch
        {
            "SampleEntity" => new List<PropertyMetadata>
            {
                new() { Name = "Id", DisplayName = "ID", Type = "Guid", Description = "Jedinečný identifikátor" },
                new() { Name = "Name", DisplayName = "Název", Type = "String", Description = "Název entity" },
                new() { Name = "Value", DisplayName = "Hodnota", Type = "Integer", Description = "Číselná hodnota" },
                new() { Name = "IsActive", DisplayName = "Aktivní", Type = "Boolean", Description = "Stav aktivity" }
            },
            "Order" => GetOrderProperties(),
            "OrderItem" => GetOrderItemProperties(),
            "Invoice" => GetInvoiceProperties(),
            "InvoiceItem" => GetInvoiceItemProperties(),
            _ => new List<PropertyMetadata>()
        };
    }

    private static List<PropertyMetadata> GetOrderProperties()
    {
        return new List<PropertyMetadata>
        {
            new() { Name = "Id", DisplayName = "ID", Type = "Guid", Description = "Jedinečný identifikátor objednávky" },
            new() { Name = "OrderNumber", DisplayName = "Číslo objednávky", Type = "String", Description = "Unikátní číslo objednávky" },
            new() { Name = "OrderDate", DisplayName = "Datum objednávky", Type = "DateTime", Description = "Datum vytvoření objednávky" },
            new() { Name = "CustomerName", DisplayName = "Jméno zákazníka", Type = "String", Description = "Jméno zákazníka" },
            new() { Name = "CustomerEmail", DisplayName = "Email zákazníka", Type = "String", Description = "Email zákazníka" },
            new() { Name = "Status", DisplayName = "Stav", Type = "OrderStatus", Description = "Aktuální stav objednávky" },
            new() { Name = "SubTotal", DisplayName = "Částka bez DPH", Type = "Decimal", Description = "Celková částka bez DPH" },
            new() { Name = "TaxAmount", DisplayName = "DPH", Type = "Decimal", Description = "Výše DPH" },
            new() { Name = "DiscountPercentage", DisplayName = "Sleva %", Type = "Decimal", Description = "Sleva v procentech" },
            new() { Name = "DiscountAmount", DisplayName = "Částka slevy", Type = "Decimal", Description = "Částka slevy" },
            new() { Name = "ShippingCost", DisplayName = "Poštovné", Type = "Decimal", Description = "Náklady na dopravu" },
            new() { Name = "TotalAmount", DisplayName = "Celková částka", Type = "Decimal", Description = "Celková částka včetně DPH" },
            new() { Name = "Currency", DisplayName = "Měna", Type = "String", Description = "Měna objednávky" },
            new() { Name = "TotalItemCount", DisplayName = "Počet položek", Type = "Integer", Description = "Celkový počet položek" },
            new() { Name = "TotalWeight", DisplayName = "Celková hmotnost", Type = "Decimal", Description = "Celková hmotnost v kg" },
            new() { Name = "IsCompleted", DisplayName = "Dokončená", Type = "Boolean", Description = "Zda je objednávka dokončená" },
            new() { Name = "IsCancelled", DisplayName = "Zrušená", Type = "Boolean", Description = "Zda je objednávka zrušená" },
            new() { Name = "DaysFromOrder", DisplayName = "Dní od objednávky", Type = "Integer", Description = "Počet dní od vytvoření" }
        };
    }

    private static List<PropertyMetadata> GetOrderItemProperties()
    {
        return new List<PropertyMetadata>
        {
            new() { Name = "Id", DisplayName = "ID", Type = "Guid", Description = "Jedinečný identifikátor položky" },
            new() { Name = "ProductCode", DisplayName = "Kód produktu", Type = "String", Description = "Kód produktu" },
            new() { Name = "ProductName", DisplayName = "Název produktu", Type = "String", Description = "Název produktu" },
            new() { Name = "Category", DisplayName = "Kategorie", Type = "String", Description = "Kategorie produktu" },
            new() { Name = "UnitPrice", DisplayName = "Jednotková cena", Type = "Decimal", Description = "Cena za jednotku bez DPH" },
            new() { Name = "Quantity", DisplayName = "Množství", Type = "Integer", Description = "Objednané množství" },
            new() { Name = "Weight", DisplayName = "Hmotnost", Type = "Decimal", Description = "Hmotnost jedné jednotky v kg" },
            new() { Name = "TaxRate", DisplayName = "Sazba DPH", Type = "Decimal", Description = "Sazba DPH v procentech" },
            new() { Name = "DiscountPercentage", DisplayName = "Sleva %", Type = "Decimal", Description = "Sleva na položku v procentech" },
            new() { Name = "LineTotal", DisplayName = "Celkem bez DPH", Type = "Decimal", Description = "Celková cena bez DPH" },
            new() { Name = "LineTotalWithTax", DisplayName = "Celkem s DPH", Type = "Decimal", Description = "Celková cena včetně DPH" },
            new() { Name = "TotalWeight", DisplayName = "Celková hmotnost", Type = "Decimal", Description = "Celková hmotnost položky" },
            new() { Name = "IsExpensive", DisplayName = "Drahá položka", Type = "Boolean", Description = "Zda je položka drahá (nad 10 000 Kč)" },
            new() { Name = "IsHeavy", DisplayName = "Těžká položka", Type = "Boolean", Description = "Zda je položka těžká (nad 5 kg)" }
        };
    }

    private static List<PropertyMetadata> GetInvoiceProperties()
    {
        return new List<PropertyMetadata>
        {
            new() { Name = "Id", DisplayName = "ID", Type = "Guid", Description = "Jedinečný identifikátor faktury" },
            new() { Name = "InvoiceNumber", DisplayName = "Číslo faktury", Type = "String", Description = "Unikátní číslo faktury" },
            new() { Name = "IssueDate", DisplayName = "Datum vystavení", Type = "DateTime", Description = "Datum vystavení faktury" },
            new() { Name = "DueDate", DisplayName = "Datum splatnosti", Type = "DateTime", Description = "Datum splatnosti" },
            new() { Name = "CustomerName", DisplayName = "Jméno zákazníka", Type = "String", Description = "Jméno zákazníka" },
            new() { Name = "Type", DisplayName = "Typ faktury", Type = "InvoiceType", Description = "Typ faktury" },
            new() { Name = "Status", DisplayName = "Stav", Type = "InvoiceStatus", Description = "Aktuální stav faktury" },
            new() { Name = "SubTotal", DisplayName = "Částka bez DPH", Type = "Decimal", Description = "Celková částka bez DPH" },
            new() { Name = "TaxAmount", DisplayName = "DPH", Type = "Decimal", Description = "Výše DPH" },
            new() { Name = "TotalAmount", DisplayName = "Celková částka", Type = "Decimal", Description = "Celková částka včetně DPH" },
            new() { Name = "PaidAmount", DisplayName = "Zaplaceno", Type = "Decimal", Description = "Zaplacená částka" },
            new() { Name = "RemainingAmount", DisplayName = "Zbývá", Type = "Decimal", Description = "Zbývající částka" },
            new() { Name = "PaymentMethod", DisplayName = "Způsob platby", Type = "PaymentMethod", Description = "Způsob platby" },
            new() { Name = "IsPaid", DisplayName = "Zaplacená", Type = "Boolean", Description = "Zda je faktura zaplacená" },
            new() { Name = "IsOverdue", DisplayName = "Po splatnosti", Type = "Boolean", Description = "Zda je faktura po splatnosti" },
            new() { Name = "DaysOverdue", DisplayName = "Dní po splatnosti", Type = "Integer", Description = "Počet dní po splatnosti" },
            new() { Name = "DaysUntilDue", DisplayName = "Dní do splatnosti", Type = "Integer", Description = "Počet dní do splatnosti" },
            new() { Name = "PaymentPercentage", DisplayName = "% zaplaceno", Type = "Decimal", Description = "Procento zaplacené částky" }
        };
    }

    private static List<PropertyMetadata> GetInvoiceItemProperties()
    {
        return new List<PropertyMetadata>
        {
            new() { Name = "Id", DisplayName = "ID", Type = "Guid", Description = "Jedinečný identifikátor položky" },
            new() { Name = "ProductCode", DisplayName = "Kód produktu", Type = "String", Description = "Kód produktu/služby" },
            new() { Name = "ProductName", DisplayName = "Název produktu", Type = "String", Description = "Název produktu/služby" },
            new() { Name = "UnitPrice", DisplayName = "Jednotková cena", Type = "Decimal", Description = "Cena za jednotku bez DPH" },
            new() { Name = "Quantity", DisplayName = "Množství", Type = "Decimal", Description = "Fakturované množství" },
            new() { Name = "TaxRate", DisplayName = "Sazba DPH", Type = "Decimal", Description = "Sazba DPH v procentech" },
            new() { Name = "DiscountPercentage", DisplayName = "Sleva %", Type = "Decimal", Description = "Sleva na položku v procentech" },
            new() { Name = "LineTotal", DisplayName = "Celkem bez DPH", Type = "Decimal", Description = "Celková cena bez DPH" },
            new() { Name = "LineTotalWithTax", DisplayName = "Celkem s DPH", Type = "Decimal", Description = "Celková cena včetně DPH" },
            new() { Name = "IsExpensive", DisplayName = "Drahá položka", Type = "Boolean", Description = "Zda je položka drahá (nad 5 000 Kč)" },
            new() { Name = "HasDiscount", DisplayName = "Má slevu", Type = "Boolean", Description = "Zda má položka slevu" }
        };
    }
}
