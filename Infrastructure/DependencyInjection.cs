﻿﻿﻿﻿﻿using Application.Abstraction;
using Application.Abstraction.Mediator;
using Application.Services.Events;
using Application.Features.Sample;
using Application.Features.Orders;
using Application.Features.Invoices;
using Domain.Entities;
using Domain.System;
using Infrastructure.Identity;
using Infrastructure.Mediator;
using Infrastructure.Persistence;
using Infrastructure.Persistence.Interceptors;
using Infrastructure.RuleEngine;
using Infrastructure.Services;
using Infrastructure.Services.Mapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Builder;
using Infrastructure.RuleEngine.API;
using Infrastructure.Services.Identity;
using Microsoft.Identity.Web;

namespace Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("DefaultConnection") ??
                               throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");

        // Registrace interceptorů
        services.AddScoped<TrackableEntityInterceptor>();
        services.AddScoped<AuditableEntityInterceptor>();

        services.AddDbContext<ApplicationDbContext>((serviceProvider, options) =>
        {
            var trackableInterceptor = serviceProvider.GetRequiredService<TrackableEntityInterceptor>();
            var auditableInterceptor = serviceProvider.GetRequiredService<AuditableEntityInterceptor>();

            options.UseSqlite(connectionString)
                   .AddInterceptors(trackableInterceptor, auditableInterceptor);
        });

        // TODO tohle proveřit v GetPagedSamplesQueryHandler
        // Registruje ApplicationDbContext jako IApplicationDbContext
        services.AddScoped<IApplicationDbContext>(provider => provider.GetRequiredService<ApplicationDbContext>());

        services.AddScoped<IMediator, Mediator.Mediator>();
        services.AddScoped<INotificationPublisher, ParallelNotificationPublisher>();
        

        services.AddIdentity<ApplicationUser, IdentityRole<int>>()
            .AddEntityFrameworkStores<ApplicationDbContext>()
            .AddDefaultTokenProviders();
        services.AddAuthentication()
            .AddMicrosoftIdentityWebApp(configuration.GetSection("AzureAd"));
        
        // Registrace služeb pro entity
        services.AddScoped<ICurrentUserService, CurrentUserService>();
        services.AddScoped<DomainEventPublisher>();
        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        services.AddScoped<ISessionInfoService, SessionInfoService>();

        services.AddMemoryCache();
        services.AddScoped<ICacheService, MemoryCacheService>();

        services.AddHttpContextAccessor();
        services.AddScoped<ICurrentUserService, CurrentUserService>();
        services.AddAuthorization(options =>
        {
            // definuj policy podle rolí
            options.AddPolicy("RequireAdmin", p => p.RequireRole("Admin"));
        });

        // Registrace mapperů
        RegisterMappers(services);

        // Registrace RuleEngine komponent
        RegisterRuleEngine(services);

        return services;
    }

    /// <summary>
    /// Registruje mappery pro entity a DTO
    /// </summary>
    private static void RegisterMappers(IServiceCollection services)
    {
        // === SampleEntity mappery ===
        services.AddSingleton<IUnifiedMapper<SampleEntity, SampleDto>>(sp =>
            new UnifiedMapper<SampleEntity, SampleDto>());

        services.AddSingleton<IUnifiedMapper<SampleAddEdit, SampleEntity>>(sp =>
            new UnifiedMapper<SampleAddEdit, SampleEntity>());

        // === OrderItem mappery (musí být před Order mappery) ===
        services.AddSingleton<IUnifiedMapper<OrderItem, OrderItemDto>>(sp =>
            new UnifiedMapper<OrderItem, OrderItemDto>());

        services.AddSingleton<IUnifiedMapper<OrderItemAddEdit, OrderItem>>(sp =>
            new UnifiedMapper<OrderItemAddEdit, OrderItem>());

        // === Order mappery ===
        services.AddSingleton<IUnifiedMapper<Order, OrderDto>>(sp =>
        {
            // Použijeme automatické mapování bez vnořených kolekcí
            return new UnifiedMapper<Order, OrderDto>();
        });

        services.AddSingleton<IUnifiedMapper<OrderAddEdit, Order>>(sp =>
            new UnifiedMapper<OrderAddEdit, Order>());

        // === InvoiceItem mappery (musí být před Invoice mappery) ===
        services.AddSingleton<IUnifiedMapper<InvoiceItem, InvoiceItemDto>>(sp =>
            new UnifiedMapper<InvoiceItem, InvoiceItemDto>());

        services.AddSingleton<IUnifiedMapper<InvoiceItemAddEdit, InvoiceItem>>(sp =>
            new UnifiedMapper<InvoiceItemAddEdit, InvoiceItem>());

        // === Invoice mappery ===
        services.AddSingleton<IUnifiedMapper<Invoice, InvoiceDto>>(sp =>
        {
            // Použijeme automatické mapování bez vnořených kolekcí
            return new UnifiedMapper<Invoice, InvoiceDto>();
        });

        services.AddSingleton<IUnifiedMapper<InvoiceAddEdit, Invoice>>(sp =>
            new UnifiedMapper<InvoiceAddEdit, Invoice>());
    }

    /// <summary>
    /// Registruje komponenty RuleEngine systému
    /// </summary>
    private static void RegisterRuleEngine(IServiceCollection services)
    {
        // Registrace základních komponent
        services.AddScoped<IRuleRepository, RuleRepository>();
        services.AddScoped<IRuleDataProvider, RuleDataProvider>();

        // Registrace entity type map pro RuleEngine
        services.AddSingleton<IReadOnlyDictionary<string, Type>>(provider =>
        {
            var entityTypeMap = new Dictionary<string, Type>
            {
                // Registrace známých entit pro RuleEngine
                ["SampleEntity"] = typeof(SampleEntity),
                ["BusinessRule"] = typeof(BusinessRule),
                ["AuditTrail"] = typeof(AuditTrail),
                ["SystemLog"] = typeof(SystemLog),

                // Obchodní entity
                ["Order"] = typeof(Order),
                ["OrderItem"] = typeof(OrderItem),
                ["Invoice"] = typeof(Invoice),
                ["InvoiceItem"] = typeof(InvoiceItem)
            };

            return entityTypeMap;
        });

        // Registrace ExpressionBuilder
        services.AddScoped<IExpressionBuilder, ExpressionBuilder>();

        // Registrace CalculationEngine s vylepšenou funkcionalitou a loggingem
        services.AddScoped<CalculationEngine>();
    }

    /// <summary>
    /// Registruje RuleEngine endpointy do aplikace
    /// </summary>
    /// <param name="app">WebApplication instance</param>
    /// <returns>WebApplication pro fluent API</returns>
    public static WebApplication UseRuleEngineEndpoints(this WebApplication app)
    {
        app.MapRuleEngineEndpoints();
        return app;
    }
}