using Domain.System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Diagnostics;
using System.Text.Json;
using System.Reflection;
using Application.Abstraction;
using SharedKernel.Abstractions;
using SharedKernel.Attributes;

namespace Infrastructure.Persistence.Interceptors;

/// <summary>
/// Interceptor pro auditování změn entit označených atributem [Auditable].
/// Vytváří záznamy v AuditTrail tabulce pro sledování změn.
/// </summary>
public class AuditableEntityInterceptor : SaveChangesInterceptor
{
    private readonly ICurrentUserService _currentUserService;
    private readonly List<(AuditTrail AuditTrail, object Entity)> _pendingAuditUpdates = new();

    /// <summary>
    /// Inicializuje novou instanci AuditableEntityInterceptor.
    /// </summary>
    /// <param name="currentUserService">Služba pro získání aktuálního uživatele</param>
    public AuditableEntityInterceptor(ICurrentUserService currentUserService)
    {
        _currentUserService = currentUserService;
    }

    /// <summary>
    /// Asynchronní zpracování před uložením změn - vytváří audit záznamy.
    /// </summary>
    public override async ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData,
        InterceptionResult<int> result,
        CancellationToken cancellationToken = default)
    {
        var context = eventData.Context;
        if (context == null)
            return result;

        var now = DateTime.UtcNow;
        var user = _currentUserService.UserId ?? "anonymous";

        // Kolekce pro nové auditní záznamy
        var auditEntries = new List<AuditTrail>();
        _pendingAuditUpdates.Clear(); // Vyčistíme předchozí pending updates

        // Zpracování pouze entit označených atributem [Auditable]
        foreach (var entry in context.ChangeTracker.Entries())
        {
            // Kontrola, zda je entita označena jako auditovatelná
            if (IsAuditable(entry.Entity.GetType()) &&
                (entry.State == EntityState.Added ||
                 entry.State == EntityState.Modified ||
                 entry.State == EntityState.Deleted))
            {
                // Vytvoření auditní položky
                var idValue = GetEntityId(entry);
                var changesJson = SerializeChanges(entry);

                var auditTrail = new AuditTrail
                {
                    EntityName = entry.Entity.GetType().Name,
                    EntityId = idValue,
                    Operation = entry.State.ToString(),
                    Timestamp = now,
                    UserName = user,
                    ChangesJson = changesJson,
                    Id = 0
                };

                auditEntries.Add(auditTrail);

                // Pro nové entity si uložíme referenci pro pozdější aktualizaci EntityId
                if (entry.State == EntityState.Added)
                {
                    _pendingAuditUpdates.Add((auditTrail, entry.Entity));
                }
            }
        }

        // Přidání všech auditních záznamů do kontextu
        if (auditEntries.Any())
        {
            context.Set<AuditTrail>().AddRange(auditEntries);
        }

        return await base.SavingChangesAsync(eventData, result, cancellationToken);
    }

    /// <summary>
    /// Asynchronní zpracování po uložení změn - aktualizuje EntityId pro nové entity.
    /// </summary>
    public override async ValueTask<int> SavedChangesAsync(
        SaveChangesCompletedEventData eventData,
        int result,
        CancellationToken cancellationToken = default)
    {
        var context = eventData.Context;
        if (context != null && _pendingAuditUpdates.Any())
        {
            // Aktualizujeme EntityId pro nové entity
            foreach (var (auditTrail, entity) in _pendingAuditUpdates)
            {
                var entityId = GetEntityIdFromEntity(entity);
                if (entityId != null)
                {
                    auditTrail.EntityId = entityId;
                }
            }

            // Uložíme aktualizace audit záznamů
            await context.SaveChangesAsync(cancellationToken);
            _pendingAuditUpdates.Clear();
        }

        return await base.SavedChangesAsync(eventData, result, cancellationToken);
    }

    /// <summary>
    /// Kontroluje, zda je entita označena atributem [Auditable].
    /// </summary>
    private static bool IsAuditable(Type entityType)
    {
        return entityType.GetCustomAttribute<AuditableAttribute>() != null;
    }

    /// <summary>
    /// Získá ID entity jako string. Pro nové záznamy vrací NULL.
    /// </summary>
    private static string? GetEntityId(EntityEntry entry)
    {
        // Pro nové záznamy (Added) vracíme NULL, protože ID ještě neexistuje
        if (entry.State == EntityState.Added)
        {
            return null;
        }

        var keyProperty = entry.Properties.FirstOrDefault(p => p.Metadata.IsPrimaryKey());
        return keyProperty?.CurrentValue?.ToString();
    }

    /// <summary>
    /// Serializuje změny entity do JSON, respektuje atribut [NotAudited].
    /// </summary>
    private static string SerializeChanges(EntityEntry entry)
    {
        var entityType = entry.Entity.GetType();

        if (entry.State == EntityState.Added)
        {
            var after = GetAuditableProperties(entry, entityType)
                .ToDictionary(p => p.Name, p => entry.CurrentValues[p]);
            return JsonSerializer.Serialize(new { Before = (object?)null, After = after });
        }

        if (entry.State == EntityState.Deleted)
        {
            var before = GetAuditableProperties(entry, entityType)
                .ToDictionary(p => p.Name, p => entry.OriginalValues[p]);
            return JsonSerializer.Serialize(new { Before = before, After = (object?)null });
        }

        // Modified - pouze změněné vlastnosti
        var diffs = entry.Properties
            .Where(p => p.IsModified && IsPropertyAuditable(entityType, p.Metadata.Name))
            .ToDictionary(
                p => p.Metadata.Name,
                p => new {
                    Before = entry.OriginalValues[p.Metadata.Name],
                    After = entry.CurrentValues[p.Metadata.Name]
                }
            );
        return JsonSerializer.Serialize(diffs);
    }

    /// <summary>
    /// Získá vlastnosti entity, které mají být auditovány (nejsou označeny [NotAudited]).
    /// </summary>
    private static IEnumerable<Microsoft.EntityFrameworkCore.Metadata.IProperty> GetAuditableProperties(
        EntityEntry entry, Type entityType)
    {
        return entry.CurrentValues.Properties
            .Where(p => IsPropertyAuditable(entityType, p.Name));
    }

    /// <summary>
    /// Kontroluje, zda má být vlastnost auditována (není označena [NotAudited]).
    /// </summary>
    private static bool IsPropertyAuditable(Type entityType, string propertyName)
    {
        var property = entityType.GetProperty(propertyName);
        return property?.GetCustomAttribute<NotAuditedAttribute>() == null;
    }

    /// <summary>
    /// Získá ID entity přímo z objektu entity (po uložení).
    /// </summary>
    private static string? GetEntityIdFromEntity(object entity)
    {
        var entityType = entity.GetType();

        // Hledáme vlastnost Id
        var idProperty = entityType.GetProperty("Id");
        if (idProperty != null)
        {
            var idValue = idProperty.GetValue(entity);
            return idValue?.ToString();
        }

        return null;
    }

}