using SharedKernel.Abstractions;

namespace Domain.Entities;

/// <summary>
/// Položka objednávky.
/// </summary>
public class OrderItem : BaseTrackableEntity<Guid>
{
    /// <summary>
    /// ID objednávky.
    /// </summary>
    public Guid OrderId { get; set; }

    /// <summary>
    /// Objednávka.
    /// </summary>
    public virtual Order Order { get; set; } = null!;

    /// <summary>
    /// Kód produktu.
    /// </summary>
    public string ProductCode { get; set; } = string.Empty;

    /// <summary>
    /// Název produktu.
    /// </summary>
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// Popis produktu.
    /// </summary>
    public string? ProductDescription { get; set; }

    /// <summary>
    /// Kategorie produktu.
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// Jednotková cena bez DPH.
    /// </summary>
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// Množství.
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// Jednotka měření.
    /// </summary>
    public string Unit { get; set; } = "ks";

    /// <summary>
    /// Hmotnost jedné jednotky v kg.
    /// </summary>
    public decimal Weight { get; set; }

    /// <summary>
    /// Sazba DPH v procentech.
    /// </summary>
    public decimal TaxRate { get; set; } = 21;

    /// <summary>
    /// Sleva na položku v procentech.
    /// </summary>
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// Celková cena bez DPH (UnitPrice * Quantity - sleva).
    /// </summary>
    public decimal LineTotal { get; set; }

    /// <summary>
    /// Výše DPH pro tuto položku.
    /// </summary>
    public decimal LineTaxAmount { get; set; }

    /// <summary>
    /// Celková cena včetně DPH.
    /// </summary>
    public decimal LineTotalWithTax { get; set; }

    /// <summary>
    /// Poznámky k položce.
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Určuje, zda je produkt na skladě.
    /// </summary>
    public bool IsInStock { get; set; } = true;

    /// <summary>
    /// Očekávané datum dodání této položky.
    /// </summary>
    public DateTime? ExpectedDeliveryDate { get; set; }

    /// <summary>
    /// Vypočítá celkovou hmotnost této položky.
    /// </summary>
    public decimal TotalWeight => Weight * Quantity;

    /// <summary>
    /// Vypočítá částku slevy.
    /// </summary>
    public decimal DiscountAmount => (UnitPrice * Quantity) * (DiscountPercentage / 100);

    /// <summary>
    /// Určuje, zda je položka drahá (nad 10 000 Kč).
    /// </summary>
    public bool IsExpensive => LineTotalWithTax > 10000;

    /// <summary>
    /// Určuje, zda je položka těžká (nad 5 kg).
    /// </summary>
    public bool IsHeavy => TotalWeight > 5;
}
