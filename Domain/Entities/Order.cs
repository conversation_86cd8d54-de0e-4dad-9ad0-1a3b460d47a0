using SharedKernel.Abstractions;

namespace Domain.Entities;

/// <summary>
/// Objednávka zákazníka.
/// </summary>
public class Order : BaseTrackableEntity<Guid>
{
    /// <summary>
    /// <PERSON><PERSON><PERSON> ob<PERSON>.
    /// </summary>
    public string OrderNumber { get; set; } = string.Empty;

    /// <summary>
    /// Datum vytvoření objednávky.
    /// </summary>
    public DateTime OrderDate { get; set; }

    /// <summary>
    /// ID zákazníka.
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// J<PERSON>no zákazníka.
    /// </summary>
    public string CustomerName { get; set; } = string.Empty;

    /// <summary>
    /// Email zákazníka.
    /// </summary>
    public string CustomerEmail { get; set; } = string.Empty;

    /// <summary>
    /// Stav objednávky.
    /// </summary>
    public OrderStatus Status { get; set; }

    /// <summary>
    /// <PERSON>lk<PERSON> částka bez DPH.
    /// </summary>
    public decimal SubTotal { get; set; }

    /// <summary>
    /// Výše DPH.
    /// </summary>
    public decimal TaxAmount { get; set; }

    /// <summary>
    /// Sleva v procentech.
    /// </summary>
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// Částka slevy.
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// Poštovné.
    /// </summary>
    public decimal ShippingCost { get; set; }

    /// <summary>
    /// Celková částka včetně DPH.
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// Měna objednávky.
    /// </summary>
    public string Currency { get; set; } = "CZK";

    /// <summary>
    /// Poznámky k objednávce.
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Dodací adresa.
    /// </summary>
    public string ShippingAddress { get; set; } = string.Empty;

    /// <summary>
    /// Město dodání.
    /// </summary>
    public string ShippingCity { get; set; } = string.Empty;

    /// <summary>
    /// PSČ dodání.
    /// </summary>
    public string ShippingPostalCode { get; set; } = string.Empty;

    /// <summary>
    /// Země dodání.
    /// </summary>
    public string ShippingCountry { get; set; } = "CZ";

    /// <summary>
    /// Očekávané datum dodání.
    /// </summary>
    public DateTime? ExpectedDeliveryDate { get; set; }

    /// <summary>
    /// Skutečné datum dodání.
    /// </summary>
    public DateTime? ActualDeliveryDate { get; set; }

    /// <summary>
    /// Položky objednávky.
    /// </summary>
    public virtual ICollection<OrderItem> Items { get; set; } = new List<OrderItem>();

    /// <summary>
    /// Faktury vystavené k této objednávce.
    /// </summary>
    public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();

    /// <summary>
    /// Vypočítá celkový počet položek v objednávce.
    /// </summary>
    public int TotalItemCount => Items.Sum(i => i.Quantity);

    /// <summary>
    /// Vypočítá celkovou hmotnost objednávky.
    /// </summary>
    public decimal TotalWeight => Items.Sum(i => i.Weight * i.Quantity);

    /// <summary>
    /// Určuje, zda je objednávka dokončená.
    /// </summary>
    public bool IsCompleted => Status == OrderStatus.Delivered;

    /// <summary>
    /// Určuje, zda je objednávka zrušená.
    /// </summary>
    public bool IsCancelled => Status == OrderStatus.Cancelled;

    /// <summary>
    /// Počet dní od vytvoření objednávky.
    /// </summary>
    public int DaysFromOrder => (DateTime.Now - OrderDate).Days;
}

/// <summary>
/// Stav objednávky.
/// </summary>
public enum OrderStatus
{
    /// <summary>
    /// Nová objednávka.
    /// </summary>
    New = 0,

    /// <summary>
    /// Potvrzená objednávka.
    /// </summary>
    Confirmed = 1,

    /// <summary>
    /// Zpracovává se.
    /// </summary>
    Processing = 2,

    /// <summary>
    /// Expedována.
    /// </summary>
    Shipped = 3,

    /// <summary>
    /// Doručena.
    /// </summary>
    Delivered = 4,

    /// <summary>
    /// Zrušena.
    /// </summary>
    Cancelled = 5
}
