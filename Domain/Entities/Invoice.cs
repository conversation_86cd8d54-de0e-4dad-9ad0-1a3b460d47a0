using SharedKernel.Abstractions;

namespace Domain.Entities;

/// <summary>
/// Faktura.
/// </summary>
public class Invoice : BaseTrackableEntity<Guid>
{
    /// <summary>
    /// <PERSON><PERSON><PERSON> faktury.
    /// </summary>
    public string InvoiceNumber { get; set; } = string.Empty;

    /// <summary>
    /// Datum vystavení faktury.
    /// </summary>
    public DateTime IssueDate { get; set; }

    /// <summary>
    /// Datum splatnosti.
    /// </summary>
    public DateTime DueDate { get; set; }

    /// <summary>
    /// ID objednávky (mů<PERSON><PERSON> být null pro faktury bez objednávky).
    /// </summary>
    public Guid? OrderId { get; set; }

    /// <summary>
    /// Objednávka.
    /// </summary>
    public virtual Order? Order { get; set; }

    /// <summary>
    /// ID zákazníka.
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// J<PERSON><PERSON> zákazníka.
    /// </summary>
    public string CustomerName { get; set; } = string.Empty;

    /// <summary>
    /// Email zákazníka.
    /// </summary>
    public string CustomerEmail { get; set; } = string.Empty;

    /// <summary>
    /// Adresa zákazníka.
    /// </summary>
    public string CustomerAddress { get; set; } = string.Empty;

    /// <summary>
    /// IČO zákazníka.
    /// </summary>
    public string? CustomerTaxId { get; set; }

    /// <summary>
    /// DIČ zákazníka.
    /// </summary>
    public string? CustomerVatId { get; set; }

    /// <summary>
    /// Typ faktury.
    /// </summary>
    public InvoiceType Type { get; set; }

    /// <summary>
    /// Stav faktury.
    /// </summary>
    public InvoiceStatus Status { get; set; }

    /// <summary>
    /// Celková částka bez DPH.
    /// </summary>
    public decimal SubTotal { get; set; }

    /// <summary>
    /// Výše DPH.
    /// </summary>
    public decimal TaxAmount { get; set; }

    /// <summary>
    /// Celková částka včetně DPH.
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// Zaplacená částka.
    /// </summary>
    public decimal PaidAmount { get; set; }

    /// <summary>
    /// Zbývající částka k doplacení.
    /// </summary>
    public decimal RemainingAmount { get; set; }

    /// <summary>
    /// Měna faktury.
    /// </summary>
    public string Currency { get; set; } = "CZK";

    /// <summary>
    /// Poznámky k faktuře.
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Datum zaplacení.
    /// </summary>
    public DateTime? PaymentDate { get; set; }

    /// <summary>
    /// Způsob platby.
    /// </summary>
    public PaymentMethod PaymentMethod { get; set; }

    /// <summary>
    /// Variabilní symbol.
    /// </summary>
    public string? VariableSymbol { get; set; }

    /// <summary>
    /// Konstantní symbol.
    /// </summary>
    public string? ConstantSymbol { get; set; }

    /// <summary>
    /// Specifický symbol.
    /// </summary>
    public string? SpecificSymbol { get; set; }

    /// <summary>
    /// Položky faktury.
    /// </summary>
    public virtual ICollection<InvoiceItem> Items { get; set; } = new List<InvoiceItem>();

    /// <summary>
    /// Určuje, zda je faktura zaplacená.
    /// </summary>
    public bool IsPaid => Status == InvoiceStatus.Paid;

    /// <summary>
    /// Určuje, zda je faktura po splatnosti.
    /// </summary>
    public bool IsOverdue => !IsPaid && DateTime.Now > DueDate;

    /// <summary>
    /// Počet dní po splatnosti.
    /// </summary>
    public int DaysOverdue => IsOverdue ? (DateTime.Now - DueDate).Days : 0;

    /// <summary>
    /// Počet dní do splatnosti.
    /// </summary>
    public int DaysUntilDue => IsPaid ? 0 : Math.Max(0, (DueDate - DateTime.Now).Days);

    /// <summary>
    /// Určuje, zda je faktura částečně zaplacená.
    /// </summary>
    public bool IsPartiallyPaid => PaidAmount > 0 && PaidAmount < TotalAmount;

    /// <summary>
    /// Procento zaplacené částky.
    /// </summary>
    public decimal PaymentPercentage => TotalAmount > 0 ? (PaidAmount / TotalAmount) * 100 : 0;
}

/// <summary>
/// Typ faktury.
/// </summary>
public enum InvoiceType
{
    /// <summary>
    /// Běžná faktura.
    /// </summary>
    Standard = 0,

    /// <summary>
    /// Zálohová faktura.
    /// </summary>
    Advance = 1,

    /// <summary>
    /// Dobropis.
    /// </summary>
    CreditNote = 2,

    /// <summary>
    /// Opravný daňový doklad.
    /// </summary>
    Correction = 3
}

/// <summary>
/// Stav faktury.
/// </summary>
public enum InvoiceStatus
{
    /// <summary>
    /// Koncept.
    /// </summary>
    Draft = 0,

    /// <summary>
    /// Vystavená.
    /// </summary>
    Issued = 1,

    /// <summary>
    /// Odeslaná.
    /// </summary>
    Sent = 2,

    /// <summary>
    /// Částečně zaplacená.
    /// </summary>
    PartiallyPaid = 3,

    /// <summary>
    /// Zaplacená.
    /// </summary>
    Paid = 4,

    /// <summary>
    /// Po splatnosti.
    /// </summary>
    Overdue = 5,

    /// <summary>
    /// Stornovaná.
    /// </summary>
    Cancelled = 6
}

/// <summary>
/// Způsob platby.
/// </summary>
public enum PaymentMethod
{
    /// <summary>
    /// Bankovní převod.
    /// </summary>
    BankTransfer = 0,

    /// <summary>
    /// Hotovost.
    /// </summary>
    Cash = 1,

    /// <summary>
    /// Kreditní karta.
    /// </summary>
    CreditCard = 2,

    /// <summary>
    /// PayPal.
    /// </summary>
    PayPal = 3,

    /// <summary>
    /// Dobírka.
    /// </summary>
    CashOnDelivery = 4
}
