using SharedKernel.Abstractions;

namespace Domain.Entities;

/// <summary>
/// Položka faktury.
/// </summary>
public class InvoiceItem : BaseTrackableEntity<Guid>
{
    /// <summary>
    /// ID faktury.
    /// </summary>
    public Guid InvoiceId { get; set; }

    /// <summary>
    /// Faktura.
    /// </summary>
    public virtual Invoice Invoice { get; set; } = null!;

    /// <summary>
    /// Kód produktu/služby.
    /// </summary>
    public string ProductCode { get; set; } = string.Empty;

    /// <summary>
    /// Název produktu/služby.
    /// </summary>
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// Popis produktu/služby.
    /// </summary>
    public string? ProductDescription { get; set; }

    /// <summary>
    /// Jednotková cena bez DPH.
    /// </summary>
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// Množství.
    /// </summary>
    public decimal Quantity { get; set; }

    /// <summary>
    /// Jednotka měření.
    /// </summary>
    public string Unit { get; set; } = "ks";

    /// <summary>
    /// Sazba DPH v procentech.
    /// </summary>
    public decimal TaxRate { get; set; } = 21;

    /// <summary>
    /// Sleva na položku v procentech.
    /// </summary>
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// Celková cena bez DPH (UnitPrice * Quantity - sleva).
    /// </summary>
    public decimal LineTotal { get; set; }

    /// <summary>
    /// Výše DPH pro tuto položku.
    /// </summary>
    public decimal LineTaxAmount { get; set; }

    /// <summary>
    /// Celková cena včetně DPH.
    /// </summary>
    public decimal LineTotalWithTax { get; set; }

    /// <summary>
    /// Poznámky k položce.
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Vypočítá částku slevy.
    /// </summary>
    public decimal DiscountAmount => (UnitPrice * Quantity) * (DiscountPercentage / 100);

    /// <summary>
    /// Určuje, zda je položka drahá (nad 5 000 Kč).
    /// </summary>
    public bool IsExpensive => LineTotalWithTax > 5000;

    /// <summary>
    /// Určuje, zda má položka slevu.
    /// </summary>
    public bool HasDiscount => DiscountPercentage > 0;
}
