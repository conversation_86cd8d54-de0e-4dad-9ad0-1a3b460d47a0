using System;
using System.Reflection;
using API.ApiService;
using API.Endpoints;
using Application.Features.Sample;
using Domain.Entities;
using SharedKernel.Abstractions;
using Xunit;

namespace API.Tests;

/// <summary>
/// Unit testy pro generické CRUD komponenty
/// </summary>
public class CrudComponentsTests
{
    [Fact]
    public void CrudApiService_ShouldImplementInterface()
    {
        // Arrange & Act
        var serviceType = typeof(CrudApiService<SampleEntity>);
        var interfaceType = typeof(ICrudApiService<SampleEntity>);

        // Assert
        Assert.True(interfaceType.IsAssignableFrom(serviceType));
    }

    [Fact]
    public void CrudEndpointExtensions_ShouldHaveMapCrudEndpointsMethod()
    {
        // Arrange & Act
        var method = typeof(CrudEndpoints).GetMethod("MapCrudEndpoints");

        // Assert
        Assert.NotNull(method);
        Assert.True(method.IsStatic);
        Assert.True(method.IsGenericMethodDefinition);
        Assert.Equal(1, method.GetGenericArguments().Length); // Pouze TEntity
    }

    [Fact]
    public void ICrudApiService_ShouldHaveCorrectMethods()
    {
        // Arrange
        var interfaceType = typeof(ICrudApiService<SampleEntity>);

        // Act & Assert
        Assert.NotNull(interfaceType.GetMethod("GetAllAsync"));
        Assert.NotNull(interfaceType.GetMethod("GetPagedAsync"));
        Assert.NotNull(interfaceType.GetMethod("GetByIdAsync"));
        Assert.NotNull(interfaceType.GetMethod("CreateAsync"));
        Assert.NotNull(interfaceType.GetMethod("UpdateAsync"));
        Assert.NotNull(interfaceType.GetMethod("DeleteAsync"));
        Assert.NotNull(interfaceType.GetMethod("QueryAsync"));
    }

    [Fact]
    public void QueryAsync_ShouldHaveCorrectSignature()
    {
        // Arrange
        var interfaceType = typeof(ICrudApiService<SampleEntity>);
        var method = interfaceType.GetMethod("QueryAsync");

        // Act & Assert
        Assert.NotNull(method);

        var parameters = method.GetParameters();
        Assert.Equal(4, parameters.Length);

        // Ověření parametrů
        Assert.Equal("specification", parameters[0].Name);
        Assert.Equal("pageNumber", parameters[1].Name);
        Assert.Equal("pageSize", parameters[2].Name);
        Assert.Equal("useCache", parameters[3].Name);

        // Ověření, že parametry jsou nullable/optional
        Assert.True(parameters[0].HasDefaultValue); // specification
        Assert.True(parameters[1].HasDefaultValue); // pageNumber
        Assert.True(parameters[2].HasDefaultValue); // pageSize
        Assert.True(parameters[3].HasDefaultValue); // useCache
    }

    [Fact]
    public void CrudApiService_ShouldHaveCorrectConstraints()
    {
        // Arrange
        var serviceType = typeof(CrudApiService<>);
        var constraints = serviceType.GetGenericArguments();

        // Assert
        Assert.Single(constraints); // Pouze jeden generický parametr

        // TEntity constraint: class - ověříme, že má class constraint
        var entityParameter = constraints[0];
        var attributes = entityParameter.GenericParameterAttributes;
        Assert.True((attributes & GenericParameterAttributes.ReferenceTypeConstraint) != 0); // TEntity má class constraint
    }

    [Fact]
    public void SampleEntity_ShouldImplementIEntity()
    {
        // Arrange & Act
        var entityType = typeof(SampleEntity);
        var entityInterface = typeof(IEntity<int>);

        // Assert
        Assert.True(entityInterface.IsAssignableFrom(entityType));
    }

    [Fact]
    public void SampleAddEdit_ShouldHaveParameterlessConstructor()
    {
        // Arrange & Act
        var constructor = typeof(SampleAddEdit).GetConstructor(Type.EmptyTypes);

        // Assert
        Assert.NotNull(constructor);
        Assert.True(constructor.IsPublic);
    }
}


